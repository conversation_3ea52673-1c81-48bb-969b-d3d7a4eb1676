#!/usr/bin/env python3
"""
AI Code Assistant - 快速安装和配置脚本
"""

import os
import sys
import subprocess
import json
import shutil
from pathlib import Path
import argparse

def run_command(command, check=True):
    """运行shell命令"""
    print(f"执行命令: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"命令执行失败: {result.stderr}")
        sys.exit(1)
    return result

def check_requirements():
    """检查系统要求"""
    print("检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 检查Docker
    try:
        run_command("docker --version")
        print("✓ Docker已安装")
    except:
        print("警告: Docker未安装，将无法使用容器化部署")
    
    # 检查Node.js (用于VS Code插件开发)
    try:
        run_command("node --version")
        print("✓ Node.js已安装")
    except:
        print("警告: Node.js未安装，将无法开发VS Code插件")
    
    # 检查Java (用于IntelliJ插件开发)
    try:
        run_command("java -version")
        print("✓ Java已安装")
    except:
        print("警告: Java未安装，将无法开发IntelliJ插件")

def create_config():
    """创建配置文件"""
    print("创建配置文件...")
    
    config = {
        "api": {
            "host": "0.0.0.0",
            "port": 8000,
            "debug": False,
            "cors_origins": ["*"]
        },
        "ai_services": {
            "openai": {
                "api_key": "",
                "model": "gpt-4",
                "max_tokens": 2000
            },
            "anthropic": {
                "api_key": "",
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 2000
            }
        },
        "database": {
            "url": "postgresql://postgres:password@localhost:5432/ai_code_assistant",
            "echo": False
        },
        "redis": {
            "url": "redis://localhost:6379",
            "db": 0
        },
        "security": {
            "jwt_secret": "your-secret-key-here",
            "jwt_algorithm": "HS256",
            "jwt_expire_hours": 24
        },
        "rate_limiting": {
            "requests_per_minute": 60,
            "requests_per_hour": 1000
        },
        "logging": {
            "level": "INFO",
            "file": "logs/app.log"
        }
    }
    
    config_path = Path("config.json")
    if not config_path.exists():
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✓ 配置文件已创建: {config_path}")
    else:
        print(f"配置文件已存在: {config_path}")

def setup_python_env():
    """设置Python环境"""
    print("设置Python环境...")
    
    # 创建虚拟环境
    if not Path("venv").exists():
        run_command(f"{sys.executable} -m venv venv")
        print("✓ 虚拟环境已创建")
    
    # 激活虚拟环境并安装依赖
    if os.name == 'nt':  # Windows
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/Mac
        pip_cmd = "venv/bin/pip"
    
    run_command(f"{pip_cmd} install --upgrade pip")
    run_command(f"{pip_cmd} install -r requirements.txt")
    print("✓ Python依赖已安装")

def setup_vscode_plugin():
    """设置VS Code插件开发环境"""
    print("设置VS Code插件开发环境...")
    
    plugin_dir = Path("plugins/vscode")
    if plugin_dir.exists():
        os.chdir(plugin_dir)
        
        # 安装Node.js依赖
        run_command("npm install")
        
        # 编译TypeScript
        run_command("npm run compile")
        
        print("✓ VS Code插件开发环境已设置")
        os.chdir("../..")
    else:
        print("VS Code插件目录不存在，跳过设置")

def setup_intellij_plugin():
    """设置IntelliJ插件开发环境"""
    print("设置IntelliJ插件开发环境...")
    
    plugin_dir = Path("plugins/intellij")
    if plugin_dir.exists():
        os.chdir(plugin_dir)
        
        # 使用Gradle构建
        if os.name == 'nt':  # Windows
            run_command("gradlew.bat build")
        else:  # Unix/Linux/Mac
            run_command("./gradlew build")
        
        print("✓ IntelliJ插件开发环境已设置")
        os.chdir("../..")
    else:
        print("IntelliJ插件目录不存在，跳过设置")

def setup_database():
    """设置数据库"""
    print("设置数据库...")
    
    # 使用Docker启动PostgreSQL和Redis
    try:
        run_command("docker-compose up -d postgres redis")
        print("✓ 数据库服务已启动")
        
        # 等待数据库启动
        import time
        print("等待数据库启动...")
        time.sleep(10)
        
        # 运行数据库迁移
        if os.name == 'nt':  # Windows
            python_cmd = "venv\\Scripts\\python"
        else:  # Unix/Linux/Mac
            python_cmd = "venv/bin/python"
        
        run_command(f"{python_cmd} -m alembic upgrade head")
        print("✓ 数据库迁移已完成")
        
    except Exception as e:
        print(f"数据库设置失败: {e}")
        print("请手动启动PostgreSQL和Redis服务")

def create_directories():
    """创建必要的目录"""
    print("创建项目目录...")
    
    directories = [
        "logs",
        "core/services",
        "core/models",
        "core/utils",
        "plugins/vscode/src/services",
        "plugins/vscode/src/utils",
        "plugins/intellij/src/main/java/com/aiassistant",
        "tests/unit",
        "tests/integration",
        "docs",
        "scripts",
        "data/templates",
        "data/cache"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✓ 项目目录已创建")

def start_services():
    """启动服务"""
    print("启动AI Code Assistant服务...")
    
    try:
        # 启动API服务器
        if os.name == 'nt':  # Windows
            python_cmd = "venv\\Scripts\\python"
        else:  # Unix/Linux/Mac
            python_cmd = "venv/bin/python"
        
        print("启动API服务器...")
        print("服务将在 http://localhost:8000 上运行")
        print("API文档: http://localhost:8000/docs")
        print("按 Ctrl+C 停止服务")
        
        run_command(f"{python_cmd} -m uvicorn core.api_server:app --host 0.0.0.0 --port 8000 --reload")
        
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动服务失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="AI Code Assistant 安装和配置脚本")
    parser.add_argument("--skip-checks", action="store_true", help="跳过系统要求检查")
    parser.add_argument("--skip-python", action="store_true", help="跳过Python环境设置")
    parser.add_argument("--skip-plugins", action="store_true", help="跳过插件设置")
    parser.add_argument("--skip-database", action="store_true", help="跳过数据库设置")
    parser.add_argument("--start", action="store_true", help="安装完成后启动服务")
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("AI Code Assistant 安装程序")
    print("=" * 50)
    
    try:
        # 检查系统要求
        if not args.skip_checks:
            check_requirements()
        
        # 创建目录结构
        create_directories()
        
        # 创建配置文件
        create_config()
        
        # 设置Python环境
        if not args.skip_python:
            setup_python_env()
        
        # 设置插件开发环境
        if not args.skip_plugins:
            setup_vscode_plugin()
            setup_intellij_plugin()
        
        # 设置数据库
        if not args.skip_database:
            setup_database()
        
        print("\n" + "=" * 50)
        print("安装完成！")
        print("=" * 50)
        
        print("\n下一步:")
        print("1. 编辑 config.json 文件，添加您的AI服务API密钥")
        print("2. 运行 'python setup.py --start' 启动服务")
        print("3. 访问 http://localhost:8000/docs 查看API文档")
        print("4. 在VS Code中安装插件: 进入 plugins/vscode 目录，运行 'code .'")
        print("5. 在IntelliJ IDEA中导入插件项目: plugins/intellij")
        
        # 如果指定了启动参数，则启动服务
        if args.start:
            print("\n正在启动服务...")
            start_services()
            
    except Exception as e:
        print(f"\n安装过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
