# AI Code Assistant 实现指南

## 项目概述

这个项目实现了一个完整的AI代码助手系统，包括：
- 🚀 **核心API服务器** - 提供代码生成、分析、重构等功能
- 🔌 **VS Code插件** - 集成到VS Code编辑器
- 🔌 **IntelliJ IDEA插件** - 集成到IntelliJ系列IDE
- 🌐 **Web界面** - 浏览器端使用
- 📊 **监控和分析** - 使用统计和性能监控

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repository-url>
cd ai-code-assistant

# 运行安装脚本
python setup.py

# 或者手动安装
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. 配置API密钥

编辑 `config.json` 文件，添加您的AI服务API密钥：

```json
{
  "ai_services": {
    "openai": {
      "api_key": "your-openai-api-key-here",
      "model": "gpt-4"
    },
    "anthropic": {
      "api_key": "your-anthropic-api-key-here",
      "model": "claude-3-sonnet-20240229"
    }
  }
}
```

### 3. 启动服务

```bash
# 使用Docker (推荐)
docker-compose up -d

# 或直接运行
python -m uvicorn core.api_server:app --host 0.0.0.0 --port 8000 --reload
```

访问 http://localhost:8000/docs 查看API文档

## 核心功能实现

### 1. 代码生成引擎

**位置**: `core/services/code_generator.py`

**核心功能**:
- 自然语言需求解析
- 多语言代码生成
- 代码模板管理
- 上下文感知生成

**实现示例**:
```python
class CodeGeneratorService:
    async def generate_code(self, requirement: str, language: str, context: dict):
        # 1. 解析需求
        parsed_req = await self.nlp_processor.parse(requirement)
        
        # 2. 选择模板
        template = self.template_manager.get_template(parsed_req.type, language)
        
        # 3. 调用AI生成
        prompt = self.build_prompt(parsed_req, template, context)
        response = await self.ai_client.generate(prompt)
        
        # 4. 后处理
        code = self.post_process(response, language)
        
        return GenerateResult(code=code, explanation=response.explanation)
```

### 2. 代码分析引擎

**位置**: `core/services/code_analyzer.py`

**核心功能**:
- 静态代码分析
- 代码质量评估
- 安全漏洞检测
- 性能优化建议

**支持的分析类型**:
- **语法检查**: AST解析，语法错误检测
- **代码质量**: 复杂度分析，代码异味检测
- **安全分析**: 常见安全漏洞扫描
- **性能分析**: 性能瓶颈识别

### 3. VS Code插件开发

**位置**: `plugins/vscode/`

**主要文件**:
- `package.json` - 插件配置和依赖
- `src/extension.ts` - 插件主入口
- `src/services/` - 服务层代码

**开发步骤**:
```bash
cd plugins/vscode
npm install
npm run compile
code .  # 在VS Code中打开
# 按F5启动调试
```

**主要功能**:
- 右键菜单集成
- 命令面板命令
- 快捷键支持
- 状态栏显示
- 设置面板

### 4. IntelliJ IDEA插件开发

**位置**: `plugins/intellij/`

**技术栈**:
- Java/Kotlin
- IntelliJ Platform SDK
- Gradle构建系统

**开发步骤**:
```bash
cd plugins/intellij
./gradlew build
./gradlew runIde  # 启动测试IDE
```

## 部署方案

### 1. Docker部署 (推荐)

```bash
# 构建和启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f api-server
```

**服务组件**:
- **api-server**: 核心API服务
- **postgres**: 数据库
- **redis**: 缓存服务
- **nginx**: 反向代理
- **prometheus**: 监控
- **grafana**: 可视化仪表板

### 2. 云平台部署

**AWS部署**:
```bash
# 使用AWS ECS
aws ecs create-cluster --cluster-name ai-code-assistant
aws ecs register-task-definition --cli-input-json file://task-definition.json
aws ecs create-service --cluster ai-code-assistant --service-name api-server --task-definition ai-code-assistant
```

**Google Cloud部署**:
```bash
# 使用Google Cloud Run
gcloud run deploy ai-code-assistant --image gcr.io/PROJECT-ID/ai-code-assistant --platform managed
```

**Azure部署**:
```bash
# 使用Azure Container Instances
az container create --resource-group myResourceGroup --name ai-code-assistant --image myregistry.azurecr.io/ai-code-assistant
```

### 3. Kubernetes部署

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-code-assistant
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-code-assistant
  template:
    metadata:
      labels:
        app: ai-code-assistant
    spec:
      containers:
      - name: api-server
        image: ai-code-assistant:latest
        ports:
        - containerPort: 8000
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-secrets
              key: openai-key
```

## 插件发布

### VS Code插件发布

```bash
cd plugins/vscode

# 安装vsce工具
npm install -g vsce

# 打包插件
vsce package

# 发布到市场
vsce publish
```

### IntelliJ插件发布

```bash
cd plugins/intellij

# 构建插件
./gradlew buildPlugin

# 发布到JetBrains插件市场
./gradlew publishPlugin
```

## 监控和维护

### 1. 日志管理

**日志级别**:
- ERROR: 错误信息
- WARN: 警告信息
- INFO: 一般信息
- DEBUG: 调试信息

**日志位置**:
- 应用日志: `logs/app.log`
- 访问日志: `logs/access.log`
- 错误日志: `logs/error.log`

### 2. 性能监控

**Prometheus指标**:
- `http_requests_total`: HTTP请求总数
- `http_request_duration_seconds`: 请求响应时间
- `ai_api_calls_total`: AI API调用次数
- `code_generation_success_rate`: 代码生成成功率

**Grafana仪表板**:
- 访问 http://localhost:3000
- 用户名/密码: admin/admin
- 导入预配置的仪表板

### 3. 健康检查

```bash
# API健康检查
curl http://localhost:8000/

# 数据库连接检查
curl http://localhost:8000/health/db

# Redis连接检查
curl http://localhost:8000/health/redis
```

## 开发指南

### 1. 添加新的编程语言支持

1. 在 `core/analyzers/` 中创建新的分析器
2. 在 `core/generators/` 中添加语言特定的生成器
3. 更新 `core/models/languages.py` 中的语言定义
4. 添加相应的测试用例

### 2. 扩展AI模型支持

1. 在 `core/ai_clients/` 中实现新的客户端
2. 更新配置文件添加新模型配置
3. 在服务层中集成新客户端
4. 添加相应的错误处理

### 3. 添加新功能

1. 定义API接口 (`core/models/requests.py`, `core/models/responses.py`)
2. 实现服务逻辑 (`core/services/`)
3. 添加API路由 (`core/api_server.py`)
4. 更新插件代码支持新功能
5. 编写测试用例

## 测试

### 1. 单元测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/test_code_generator.py

# 生成覆盖率报告
pytest --cov=core --cov-report=html
```

### 2. 集成测试

```bash
# 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 运行集成测试
pytest tests/integration/

# 清理测试环境
docker-compose -f docker-compose.test.yml down
```

### 3. 插件测试

**VS Code插件测试**:
```bash
cd plugins/vscode
npm test
```

**IntelliJ插件测试**:
```bash
cd plugins/intellij
./gradlew test
```

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `config.json` 中的API密钥配置
   - 确认密钥有效且有足够的配额

2. **数据库连接失败**
   - 检查PostgreSQL服务是否运行
   - 验证数据库连接字符串

3. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis连接配置

4. **插件无法连接API**
   - 检查API服务器是否运行
   - 验证网络连接和防火墙设置

### 调试技巧

1. **启用调试模式**
   ```bash
   export DEBUG=true
   python -m uvicorn core.api_server:app --reload --log-level debug
   ```

2. **查看详细日志**
   ```bash
   tail -f logs/app.log
   ```

3. **使用API测试工具**
   - Postman
   - curl
   - httpie

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request
5. 等待代码审查

详细的贡献指南请参考 `CONTRIBUTING.md`

## 许可证

本项目采用MIT许可证，详见 `LICENSE` 文件。
