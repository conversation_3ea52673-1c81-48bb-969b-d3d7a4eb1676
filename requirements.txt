# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# AI和机器学习
openai==1.3.7
anthropic==0.7.7
transformers==4.35.2
torch==2.1.1
sentence-transformers==2.2.2
langchain==0.0.340
langchain-community==0.0.1

# 代码分析工具
ast-tools==0.1.0
pylint==3.0.3
flake8==6.1.0
black==23.11.0
mypy==1.7.1
bandit==1.7.5
radon==6.0.1

# 数据库
sqlalchemy==2.0.23
alembic==1.13.0
psycopg2-binary==2.9.9
asyncpg==0.29.0

# 缓存
redis==5.0.1
aioredis==2.0.1

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# 配置管理
python-dotenv==1.0.0
pydantic-settings==2.1.0

# 日志和监控
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# 工具库
click==8.1.7
rich==13.7.0
typer==0.9.0
jinja2==3.1.2

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# 代码质量
pre-commit==3.6.0
isort==5.12.0
autoflake==2.2.1

# 文档
mkdocs==1.5.3
mkdocs-material==9.4.8

# 部署
gunicorn==21.2.0
docker==6.1.3

# 开发工具
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6

# 数据处理
pandas==2.1.4
numpy==1.25.2
matplotlib==3.8.2
seaborn==0.13.0

# 异步任务
celery==5.3.4
kombu==5.3.4

# 文件处理
python-magic==0.4.27
chardet==5.2.0

# 网络和协议
websockets==12.0
sse-starlette==1.8.2

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 正则表达式增强
regex==2023.10.3

# JSON处理
orjson==3.9.10
ujson==5.8.0

# 环境变量
environs==10.0.0

# 限流
slowapi==0.1.9

# 模板引擎
mako==1.3.0

# 数据验证
cerberus==1.3.5
marshmallow==3.20.1

# 缓存装饰器
cachetools==5.3.2

# 异步工具
asyncio-mqtt==0.16.1
aiofiles==23.2.1

# 代码格式化
autopep8==2.0.4
yapf==0.40.2

# 静态分析
vulture==2.10
pycodestyle==2.11.1
pydocstyle==6.3.0

# 性能分析
py-spy==0.3.14
memory-profiler==0.61.0

# 国际化
babel==2.13.1

# 图像处理 (如果需要处理代码截图等)
pillow==10.1.0

# 加密
bcrypt==4.1.2
argon2-cffi==23.1.0

# 队列
rq==1.15.1

# 配置解析
toml==0.10.2
pyyaml==6.0.1

# 网络请求重试
tenacity==8.2.3

# 数据序列化
msgpack==1.0.7
pickle5==0.0.12

# 时区处理
zoneinfo==0.2.1

# 进程管理
psutil==5.9.6

# 文本处理
textdistance==4.6.0
fuzzywuzzy==0.18.0
python-levenshtein==0.23.0

# API文档
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
