{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020"], "sourceMap": true, "rootDir": "src", "strict": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"*": ["node_modules/*"]}, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "declarationMap": false}, "include": ["src/**/*"], "exclude": ["node_modules", ".vscode-test", "out"]}