#!/usr/bin/env node

/**
 * VS Code扩展错误修复脚本
 * 自动修复常见的TypeScript和依赖问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 AI Code Assistant - VS Code扩展错误修复脚本');
console.log('=' * 50);

function runCommand(command, description) {
    console.log(`\n📋 ${description}...`);
    try {
        execSync(command, { stdio: 'inherit', cwd: __dirname });
        console.log(`✅ ${description} 完成`);
        return true;
    } catch (error) {
        console.error(`❌ ${description} 失败:`, error.message);
        return false;
    }
}

function checkFile(filePath, description) {
    const fullPath = path.join(__dirname, filePath);
    if (fs.existsSync(fullPath)) {
        console.log(`✅ ${description} 存在: ${filePath}`);
        return true;
    } else {
        console.log(`❌ ${description} 缺失: ${filePath}`);
        return false;
    }
}

function createMissingDirectories() {
    console.log('\n📁 检查和创建必要目录...');
    
    const directories = [
        'src/services',
        'src/utils',
        'src/test/suite',
        'out',
        '.vscode'
    ];
    
    directories.forEach(dir => {
        const fullPath = path.join(__dirname, dir);
        if (!fs.existsSync(fullPath)) {
            fs.mkdirSync(fullPath, { recursive: true });
            console.log(`✅ 创建目录: ${dir}`);
        } else {
            console.log(`✅ 目录已存在: ${dir}`);
        }
    });
}

function fixPackageJson() {
    console.log('\n📦 检查 package.json...');
    
    const packagePath = path.join(__dirname, 'package.json');
    if (!fs.existsSync(packagePath)) {
        console.log('❌ package.json 不存在');
        return false;
    }
    
    try {
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        
        // 检查必要的脚本
        const requiredScripts = {
            'compile': 'tsc -p ./',
            'watch': 'tsc -watch -p ./',
            'pretest': 'npm run compile && npm run lint',
            'lint': 'eslint src --ext ts',
            'test': 'node ./out/test/runTest.js'
        };
        
        let modified = false;
        if (!packageJson.scripts) {
            packageJson.scripts = {};
        }
        
        Object.entries(requiredScripts).forEach(([script, command]) => {
            if (!packageJson.scripts[script]) {
                packageJson.scripts[script] = command;
                modified = true;
                console.log(`✅ 添加脚本: ${script}`);
            }
        });
        
        if (modified) {
            fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
            console.log('✅ package.json 已更新');
        }
        
        return true;
    } catch (error) {
        console.error('❌ 修复 package.json 失败:', error.message);
        return false;
    }
}

function main() {
    console.log('\n🚀 开始修复过程...\n');
    
    // 1. 创建必要目录
    createMissingDirectories();
    
    // 2. 检查关键文件
    console.log('\n📄 检查关键文件...');
    const criticalFiles = [
        ['package.json', 'Package配置文件'],
        ['tsconfig.json', 'TypeScript配置文件'],
        ['src/extension.ts', '扩展主文件'],
        ['src/services/codeGenerator.ts', '代码生成服务'],
        ['src/services/codeAnalyzer.ts', '代码分析服务'],
        ['src/utils/configuration.ts', '配置管理工具'],
        ['src/utils/logger.ts', '日志工具']
    ];
    
    let allFilesExist = true;
    criticalFiles.forEach(([file, desc]) => {
        if (!checkFile(file, desc)) {
            allFilesExist = false;
        }
    });
    
    if (!allFilesExist) {
        console.log('\n❌ 部分关键文件缺失，请确保所有文件都已创建');
        console.log('💡 提示: 运行项目根目录的 setup.py 脚本来创建完整的项目结构');
        return;
    }
    
    // 3. 修复 package.json
    fixPackageJson();
    
    // 4. 清理旧的编译文件
    if (fs.existsSync(path.join(__dirname, 'out'))) {
        runCommand('rm -rf out', '清理旧的编译文件');
    }
    
    // 5. 安装依赖
    if (!runCommand('npm install', '安装Node.js依赖')) {
        console.log('❌ 依赖安装失败，请手动运行: npm install');
        return;
    }
    
    // 6. 编译TypeScript
    if (!runCommand('npm run compile', '编译TypeScript代码')) {
        console.log('❌ TypeScript编译失败');
        console.log('💡 请检查TypeScript错误并手动修复');
        return;
    }
    
    // 7. 运行代码检查
    runCommand('npm run lint', '运行代码检查');
    
    console.log('\n' + '=' * 50);
    console.log('🎉 修复完成！');
    console.log('\n📋 下一步操作:');
    console.log('1. 在VS Code中打开此目录: code .');
    console.log('2. 按F5启动扩展开发主机');
    console.log('3. 在新窗口中测试扩展功能');
    console.log('\n💡 如果仍有问题，请查看 README.md 获取详细说明');
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { main };
