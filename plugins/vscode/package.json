{"name": "ai-code-assistant", "displayName": "AI Code Assistant", "description": "智能代码生成与检查工具", "version": "1.0.0", "publisher": "ai-code-assistant", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Programming Languages"], "keywords": ["ai", "code generation", "code analysis", "assistant", "productivity"], "activationEvents": ["onCommand:ai-assistant.generateCode", "onCommand:ai-assistant.analyzeCode", "onCommand:ai-assistant.refactorCode", "onCommand:ai-assistant.generateTests", "onCommand:ai-assistant.explainCode"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "ai-assistant.generateCode", "title": "生成代码", "category": "AI Assistant"}, {"command": "ai-assistant.analyzeCode", "title": "分析代码质量", "category": "AI Assistant"}, {"command": "ai-assistant.refactor<PERSON><PERSON>", "title": "重构建议", "category": "AI Assistant"}, {"command": "ai-assistant.generateTests", "title": "生成测试用例", "category": "AI Assistant"}, {"command": "ai-assistant.explainCode", "title": "解释代码", "category": "AI Assistant"}], "menus": {"editor/context": [{"when": "editorTextFocus", "command": "ai-assistant.generateCode", "group": "ai-assistant@1"}, {"when": "editorHasSelection", "command": "ai-assistant.analyzeCode", "group": "ai-assistant@2"}, {"when": "editorHasSelection", "command": "ai-assistant.refactor<PERSON><PERSON>", "group": "ai-assistant@3"}, {"when": "editorHasSelection", "command": "ai-assistant.generateTests", "group": "ai-assistant@4"}, {"when": "editorHasSelection", "command": "ai-assistant.explainCode", "group": "ai-assistant@5"}], "commandPalette": [{"command": "ai-assistant.generateCode", "when": "editorIsOpen"}, {"command": "ai-assistant.analyzeCode", "when": "editorIsOpen"}, {"command": "ai-assistant.refactor<PERSON><PERSON>", "when": "editorIsOpen"}, {"command": "ai-assistant.generateTests", "when": "editorIsOpen"}, {"command": "ai-assistant.explainCode", "when": "editorIsOpen"}]}, "configuration": {"title": "AI Code Assistant", "properties": {"ai-assistant.apiEndpoint": {"type": "string", "default": "http://localhost:8000", "description": "API服务器地址"}, "ai-assistant.apiKey": {"type": "string", "default": "", "description": "API密钥"}, "ai-assistant.defaultLanguage": {"type": "string", "default": "auto", "enum": ["auto", "javascript", "typescript", "python", "java", "csharp", "go", "rust"], "description": "默认编程语言"}, "ai-assistant.enableAutoAnalysis": {"type": "boolean", "default": false, "description": "启用自动代码分析"}, "ai-assistant.maxCodeLength": {"type": "number", "default": 10000, "description": "最大代码长度限制"}}}, "keybindings": [{"command": "ai-assistant.generateCode", "key": "ctrl+shift+g", "mac": "cmd+shift+g", "when": "editorTextFocus"}, {"command": "ai-assistant.analyzeCode", "key": "ctrl+shift+a", "mac": "cmd+shift+a", "when": "editorHasSelection"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "vsce": "^2.15.0"}, "dependencies": {"axios": "^1.2.0", "uuid": "^9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/ai-code-assistant.git"}, "bugs": {"url": "https://github.com/your-username/ai-code-assistant/issues"}, "homepage": "https://github.com/your-username/ai-code-assistant#readme", "license": "MIT"}