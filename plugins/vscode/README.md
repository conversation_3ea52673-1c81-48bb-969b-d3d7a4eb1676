# AI Code Assistant - VS Code Extension

## 解决TypeScript错误的步骤

如果您在extension.ts中看到错误，请按照以下步骤解决：

### 1. 安装依赖

```bash
cd plugins/vscode
npm install
```

### 2. 编译TypeScript

```bash
npm run compile
```

### 3. 在VS Code中打开项目

```bash
code .
```

### 4. 运行和调试

1. 按 `F5` 启动扩展开发主机
2. 或者使用命令面板 (`Ctrl+Shift+P`) 运行 "Debug: Start Debugging"

### 5. 测试扩展功能

在新打开的VS Code窗口中：

1. 打开任意代码文件
2. 右键选择 "AI Assistant" 相关选项
3. 或使用命令面板搜索 "AI Assistant"

## 常见错误解决方案

### 错误1: 找不到模块

**问题**: `Cannot find module './services/codeGenerator'`

**解决方案**: 确保所有依赖文件都已创建，运行：
```bash
npm run compile
```

### 错误2: TypeScript编译错误

**问题**: 类型定义错误

**解决方案**: 
1. 检查 `tsconfig.json` 配置
2. 确保所有 `@types/*` 包已安装
3. 运行 `npm install` 重新安装依赖

### 错误3: API连接失败

**问题**: 插件无法连接到后端API

**解决方案**:
1. 确保后端API服务器正在运行 (http://localhost:8000)
2. 在VS Code设置中配置正确的API端点
3. 检查API密钥配置

## 配置说明

### 扩展设置

在VS Code设置中搜索 "AI Assistant" 或直接编辑 `settings.json`:

```json
{
    "ai-assistant.apiEndpoint": "http://localhost:8000",
    "ai-assistant.apiKey": "your-api-key-here",
    "ai-assistant.defaultLanguage": "auto",
    "ai-assistant.enableAutoAnalysis": false,
    "ai-assistant.maxCodeLength": 10000
}
```

### 快捷键

- `Ctrl+Shift+G` (Mac: `Cmd+Shift+G`) - 生成代码
- `Ctrl+Shift+A` (Mac: `Cmd+Shift+A`) - 分析代码

## 开发指南

### 项目结构

```
plugins/vscode/
├── src/
│   ├── extension.ts          # 主入口文件
│   ├── services/            # 服务层
│   │   ├── codeGenerator.ts # 代码生成客户端
│   │   └── codeAnalyzer.ts  # 代码分析客户端
│   ├── utils/               # 工具类
│   │   ├── configuration.ts # 配置管理
│   │   └── logger.ts        # 日志工具
│   └── test/                # 测试文件
├── package.json             # 扩展配置
├── tsconfig.json           # TypeScript配置
└── README.md               # 说明文档
```

### 添加新功能

1. 在 `package.json` 中注册新命令
2. 在 `extension.ts` 中实现命令处理函数
3. 在相应的服务类中添加API调用
4. 编写测试用例

### 调试技巧

1. **查看日志**: 
   - 打开输出面板 (`Ctrl+Shift+U`)
   - 选择 "AI Code Assistant" 频道

2. **断点调试**:
   - 在代码中设置断点
   - 按 `F5` 启动调试
   - 在扩展开发主机中触发功能

3. **控制台输出**:
   - 使用 `console.log()` 输出调试信息
   - 在开发者工具中查看 (`Help > Toggle Developer Tools`)

## 发布扩展

### 1. 构建扩展包

```bash
npm run package
```

### 2. 发布到市场

```bash
npm run publish
```

### 3. 本地安装

```bash
code --install-extension ai-code-assistant-1.0.0.vsix
```

## 故障排除

### 问题: 扩展无法激活

**检查项**:
1. 查看开发者控制台是否有错误
2. 检查 `package.json` 中的激活事件
3. 确认所有依赖都已正确安装

### 问题: API调用失败

**检查项**:
1. 后端服务是否运行
2. 网络连接是否正常
3. API密钥是否正确配置
4. 请求格式是否符合API规范

### 问题: TypeScript编译错误

**解决步骤**:
1. 删除 `out` 目录
2. 运行 `npm run clean`
3. 重新编译 `npm run compile`
4. 检查类型定义是否正确

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License
