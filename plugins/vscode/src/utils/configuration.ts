import * as vscode from 'vscode';

export class ConfigurationManager {
    private static readonly EXTENSION_NAME = 'ai-assistant';

    /**
     * 获取配置值
     * @param key 配置键名
     * @param defaultValue 默认值
     * @returns 配置值
     */
    get<T>(key: string, defaultValue?: T): T {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.EXTENSION_NAME);
        return config.get<T>(key, defaultValue as T);
    }

    /**
     * 设置配置值
     * @param key 配置键名
     * @param value 配置值
     * @param target 配置目标（全局或工作区）
     */
    async set<T>(key: string, value: T, target: vscode.ConfigurationTarget = vscode.ConfigurationTarget.Global): Promise<void> {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.EXTENSION_NAME);
        await config.update(key, value, target);
    }

    /**
     * 获取API端点
     */
    getApiEndpoint(): string {
        return this.get('apiEndpoint', 'http://localhost:8000');
    }

    /**
     * 获取API密钥
     */
    getApiKey(): string {
        return this.get('apiKey', '');
    }

    /**
     * 获取默认编程语言
     */
    getDefaultLanguage(): string {
        return this.get('defaultLanguage', 'auto');
    }

    /**
     * 是否启用自动分析
     */
    isAutoAnalysisEnabled(): boolean {
        return this.get('enableAutoAnalysis', false);
    }

    /**
     * 获取最大代码长度限制
     */
    getMaxCodeLength(): number {
        return this.get('maxCodeLength', 10000);
    }

    /**
     * 获取请求超时时间（毫秒）
     */
    getRequestTimeout(): number {
        return this.get('requestTimeout', 30000);
    }

    /**
     * 是否启用调试模式
     */
    isDebugEnabled(): boolean {
        return this.get('enableDebug', false);
    }

    /**
     * 获取代码生成选项
     */
    getGenerationOptions(): any {
        return this.get('generationOptions', {
            includeComments: true,
            includeTypeHints: true,
            followConventions: true
        });
    }

    /**
     * 获取分析选项
     */
    getAnalysisOptions(): any {
        return this.get('analysisOptions', {
            checkSyntax: true,
            checkQuality: true,
            checkSecurity: true,
            checkPerformance: true
        });
    }

    /**
     * 验证配置是否完整
     */
    validateConfiguration(): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        const apiEndpoint = this.getApiEndpoint();
        if (!apiEndpoint) {
            errors.push('API端点未配置');
        } else if (!this.isValidUrl(apiEndpoint)) {
            errors.push('API端点格式无效');
        }

        const apiKey = this.getApiKey();
        if (!apiKey) {
            errors.push('API密钥未配置');
        }

        const maxCodeLength = this.getMaxCodeLength();
        if (maxCodeLength <= 0) {
            errors.push('最大代码长度必须大于0');
        }

        const requestTimeout = this.getRequestTimeout();
        if (requestTimeout <= 0) {
            errors.push('请求超时时间必须大于0');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 重置配置为默认值
     */
    async resetToDefaults(): Promise<void> {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.EXTENSION_NAME);
        
        await config.update('apiEndpoint', 'http://localhost:8000', vscode.ConfigurationTarget.Global);
        await config.update('apiKey', '', vscode.ConfigurationTarget.Global);
        await config.update('defaultLanguage', 'auto', vscode.ConfigurationTarget.Global);
        await config.update('enableAutoAnalysis', false, vscode.ConfigurationTarget.Global);
        await config.update('maxCodeLength', 10000, vscode.ConfigurationTarget.Global);
        await config.update('requestTimeout', 30000, vscode.ConfigurationTarget.Global);
        await config.update('enableDebug', false, vscode.ConfigurationTarget.Global);
    }

    /**
     * 获取所有配置
     */
    getAllConfigurations(): any {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.EXTENSION_NAME);
        return {
            apiEndpoint: this.getApiEndpoint(),
            apiKey: this.getApiKey() ? '***' : '', // 隐藏API密钥
            defaultLanguage: this.getDefaultLanguage(),
            enableAutoAnalysis: this.isAutoAnalysisEnabled(),
            maxCodeLength: this.getMaxCodeLength(),
            requestTimeout: this.getRequestTimeout(),
            enableDebug: this.isDebugEnabled(),
            generationOptions: this.getGenerationOptions(),
            analysisOptions: this.getAnalysisOptions()
        };
    }

    /**
     * 监听配置变化
     */
    onConfigurationChanged(callback: (event: vscode.ConfigurationChangeEvent) => void): vscode.Disposable {
        return vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration(ConfigurationManager.EXTENSION_NAME)) {
                callback(event);
            }
        });
    }

    private isValidUrl(url: string): boolean {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }
}
