import * as vscode from 'vscode';

export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}

export class Logger {
    private outputChannel: vscode.OutputChannel;
    private logLevel: LogLevel;
    private name: string;

    constructor(name: string, logLevel: LogLevel = LogLevel.INFO) {
        this.name = name;
        this.logLevel = logLevel;
        this.outputChannel = vscode.window.createOutputChannel(name);
    }

    /**
     * 设置日志级别
     */
    setLogLevel(level: LogLevel): void {
        this.logLevel = level;
    }

    /**
     * 记录调试信息
     */
    debug(message: string, ...args: any[]): void {
        if (this.logLevel <= LogLevel.DEBUG) {
            this.log(LogLevel.DEBUG, message, ...args);
        }
    }

    /**
     * 记录一般信息
     */
    info(message: string, ...args: any[]): void {
        if (this.logLevel <= LogLevel.INFO) {
            this.log(LogLevel.INFO, message, ...args);
        }
    }

    /**
     * 记录警告信息
     */
    warn(message: string, ...args: any[]): void {
        if (this.logLevel <= LogLevel.WARN) {
            this.log(LogLevel.WARN, message, ...args);
        }
    }

    /**
     * 记录错误信息
     */
    error(message: string, ...args: any[]): void {
        if (this.logLevel <= LogLevel.ERROR) {
            this.log(LogLevel.ERROR, message, ...args);
        }
    }

    /**
     * 显示输出面板
     */
    show(): void {
        this.outputChannel.show();
    }

    /**
     * 隐藏输出面板
     */
    hide(): void {
        this.outputChannel.hide();
    }

    /**
     * 清空日志
     */
    clear(): void {
        this.outputChannel.clear();
    }

    /**
     * 释放资源
     */
    dispose(): void {
        this.outputChannel.dispose();
    }

    private log(level: LogLevel, message: string, ...args: any[]): void {
        const timestamp = new Date().toISOString();
        const levelStr = LogLevel[level];
        const formattedMessage = this.formatMessage(message, ...args);
        
        const logEntry = `[${timestamp}] [${levelStr}] ${formattedMessage}`;
        
        // 输出到VS Code输出面板
        this.outputChannel.appendLine(logEntry);
        
        // 同时输出到控制台（开发时有用）
        switch (level) {
            case LogLevel.DEBUG:
                console.debug(`[${this.name}]`, formattedMessage);
                break;
            case LogLevel.INFO:
                console.info(`[${this.name}]`, formattedMessage);
                break;
            case LogLevel.WARN:
                console.warn(`[${this.name}]`, formattedMessage);
                break;
            case LogLevel.ERROR:
                console.error(`[${this.name}]`, formattedMessage);
                break;
        }
    }

    private formatMessage(message: string, ...args: any[]): string {
        if (args.length === 0) {
            return message;
        }

        // 简单的字符串格式化
        let formattedMessage = message;
        args.forEach((arg, index) => {
            const placeholder = `{${index}}`;
            if (formattedMessage.includes(placeholder)) {
                formattedMessage = formattedMessage.replace(placeholder, this.stringify(arg));
            } else {
                // 如果没有占位符，就追加到消息末尾
                formattedMessage += ` ${this.stringify(arg)}`;
            }
        });

        return formattedMessage;
    }

    private stringify(value: any): string {
        if (value === null) {
            return 'null';
        }
        if (value === undefined) {
            return 'undefined';
        }
        if (typeof value === 'string') {
            return value;
        }
        if (typeof value === 'number' || typeof value === 'boolean') {
            return String(value);
        }
        if (value instanceof Error) {
            return `${value.name}: ${value.message}\n${value.stack}`;
        }
        if (typeof value === 'object') {
            try {
                return JSON.stringify(value, null, 2);
            } catch {
                return String(value);
            }
        }
        return String(value);
    }
}

// 创建全局日志实例
export const globalLogger = new Logger('AI Code Assistant');

// 便捷的日志函数
export function debug(message: string, ...args: any[]): void {
    globalLogger.debug(message, ...args);
}

export function info(message: string, ...args: any[]): void {
    globalLogger.info(message, ...args);
}

export function warn(message: string, ...args: any[]): void {
    globalLogger.warn(message, ...args);
}

export function error(message: string, ...args: any[]): void {
    globalLogger.error(message, ...args);
}
