import * as vscode from 'vscode';
import { CodeGeneratorClient } from './services/codeGenerator';
import { CodeAnalyzerClient } from './services/codeAnalyzer';
import { ConfigurationManager } from './utils/configuration';
import { Logger } from './utils/logger';

let codeGenerator: CodeGeneratorClient;
let codeAnalyzer: CodeAnalyzerClient;
let logger: Logger;

export function activate(context: vscode.ExtensionContext) {
    logger = new Logger('AI Code Assistant');
    logger.info('AI Code Assistant 插件正在激活...');

    try {
        // 初始化服务
        const config = new ConfigurationManager();
        codeGenerator = new CodeGeneratorClient(config);
        codeAnalyzer = new CodeAnalyzerClient(config);

        // 注册命令
        registerCommands(context);

        // 注册事件监听器
        registerEventListeners(context);

        logger.info('AI Code Assistant 插件激活完成');
    } catch (error) {
        logger.error('插件激活失败:', error);
        vscode.window.showErrorMessage('AI Code Assistant 插件激活失败');
    }
}

function registerCommands(context: vscode.ExtensionContext) {
    // 生成代码命令
    const generateCodeCommand = vscode.commands.registerCommand(
        'ai-assistant.generateCode',
        async () => {
            try {
                await handleGenerateCode();
            } catch (error) {
                logger.error('生成代码失败:', error);
                vscode.window.showErrorMessage(`生成代码失败: ${error}`);
            }
        }
    );

    // 分析代码命令
    const analyzeCodeCommand = vscode.commands.registerCommand(
        'ai-assistant.analyzeCode',
        async () => {
            try {
                await handleAnalyzeCode();
            } catch (error) {
                logger.error('分析代码失败:', error);
                vscode.window.showErrorMessage(`分析代码失败: ${error}`);
            }
        }
    );

    // 重构代码命令
    const refactorCodeCommand = vscode.commands.registerCommand(
        'ai-assistant.refactorCode',
        async () => {
            try {
                await handleRefactorCode();
            } catch (error) {
                logger.error('重构代码失败:', error);
                vscode.window.showErrorMessage(`重构代码失败: ${error}`);
            }
        }
    );

    // 生成测试用例命令
    const generateTestsCommand = vscode.commands.registerCommand(
        'ai-assistant.generateTests',
        async () => {
            try {
                await handleGenerateTests();
            } catch (error) {
                logger.error('生成测试用例失败:', error);
                vscode.window.showErrorMessage(`生成测试用例失败: ${error}`);
            }
        }
    );

    // 解释代码命令
    const explainCodeCommand = vscode.commands.registerCommand(
        'ai-assistant.explainCode',
        async () => {
            try {
                await handleExplainCode();
            } catch (error) {
                logger.error('解释代码失败:', error);
                vscode.window.showErrorMessage(`解释代码失败: ${error}`);
            }
        }
    );

    context.subscriptions.push(
        generateCodeCommand,
        analyzeCodeCommand,
        refactorCodeCommand,
        generateTestsCommand,
        explainCodeCommand
    );
}

function registerEventListeners(context: vscode.ExtensionContext) {
    // 监听配置变化
    const configChangeListener = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('ai-assistant')) {
            logger.info('配置已更改，重新初始化服务...');
            const config = new ConfigurationManager();
            codeGenerator.updateConfig(config);
            codeAnalyzer.updateConfig(config);
        }
    });

    // 监听文档保存事件（用于自动分析）
    const documentSaveListener = vscode.workspace.onDidSaveTextDocument(async document => {
        const config = new ConfigurationManager();
        if (config.get('enableAutoAnalysis')) {
            try {
                await performAutoAnalysis(document);
            } catch (error) {
                logger.error('自动分析失败:', error);
            }
        }
    });

    context.subscriptions.push(configChangeListener, documentSaveListener);
}

async function handleGenerateCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showWarningMessage('请先打开一个文件');
        return;
    }

    // 获取用户输入的需求描述
    const requirement = await vscode.window.showInputBox({
        prompt: '请描述您需要生成的代码功能',
        placeHolder: '例如：创建一个计算斐波那契数列的函数',
        validateInput: (value) => {
            if (!value || value.trim().length === 0) {
                return '请输入代码需求描述';
            }
            return null;
        }
    });

    if (!requirement) {
        return;
    }

    // 显示进度条
    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: '正在生成代码...',
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0, message: '分析需求中...' });

        const context = getEditorContext(editor);
        const language = editor.document.languageId;

        progress.report({ increment: 30, message: '调用AI服务...' });

        const result = await codeGenerator.generateCode(requirement, language, context);

        progress.report({ increment: 70, message: '插入代码...' });

        await insertGeneratedCode(editor, result);

        progress.report({ increment: 100, message: '完成!' });
    });

    vscode.window.showInformationMessage('代码生成完成！');
}

async function handleAnalyzeCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showWarningMessage('请先打开一个文件');
        return;
    }

    const selection = editor.selection;
    const code = selection.isEmpty 
        ? editor.document.getText() 
        : editor.document.getText(selection);

    if (!code.trim()) {
        vscode.window.showWarningMessage('没有选中代码或文件为空');
        return;
    }

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: '正在分析代码...',
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0, message: '准备分析...' });

        const language = editor.document.languageId;
        
        progress.report({ increment: 50, message: '执行分析...' });

        const result = await codeAnalyzer.analyzeCode(code, language);

        progress.report({ increment: 100, message: '分析完成!' });

        await showAnalysisResults(result);
    });
}

async function handleRefactorCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showWarningMessage('请先选中需要重构的代码');
        return;
    }

    const selection = editor.selection;
    if (selection.isEmpty) {
        vscode.window.showWarningMessage('请先选中需要重构的代码');
        return;
    }

    const code = editor.document.getText(selection);
    const language = editor.document.languageId;

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: '正在生成重构建议...',
        cancellable: false
    }, async (progress) => {
        const result = await codeGenerator.refactorCode(code, language);
        await showRefactorSuggestions(editor, selection, result);
    });
}

async function handleGenerateTests() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showWarningMessage('请先选中需要生成测试的代码');
        return;
    }

    const selection = editor.selection;
    const code = selection.isEmpty 
        ? editor.document.getText() 
        : editor.document.getText(selection);

    const language = editor.document.languageId;

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: '正在生成测试用例...',
        cancellable: false
    }, async (progress) => {
        const result = await codeGenerator.generateTests(code, language);
        await createTestFile(editor, result);
    });
}

async function handleExplainCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showWarningMessage('请先选中需要解释的代码');
        return;
    }

    const selection = editor.selection;
    if (selection.isEmpty) {
        vscode.window.showWarningMessage('请先选中需要解释的代码');
        return;
    }

    const code = editor.document.getText(selection);
    const language = editor.document.languageId;

    const result = await codeAnalyzer.explainCode(code, language);
    await showCodeExplanation(result);
}

function getEditorContext(editor: vscode.TextEditor) {
    const document = editor.document;
    const position = editor.selection.active;
    
    return {
        fileName: document.fileName,
        language: document.languageId,
        currentLine: position.line,
        currentColumn: position.character,
        surroundingCode: getSurroundingCode(document, position),
        imports: extractImports(document.getText()),
        projectStructure: getProjectStructure()
    };
}

function getSurroundingCode(document: vscode.TextDocument, position: vscode.Position) {
    const startLine = Math.max(0, position.line - 10);
    const endLine = Math.min(document.lineCount - 1, position.line + 10);
    const range = new vscode.Range(startLine, 0, endLine, document.lineAt(endLine).text.length);
    return document.getText(range);
}

function extractImports(code: string): string[] {
    const imports: string[] = [];
    const lines = code.split('\n');
    
    for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed.startsWith('import ') || trimmed.startsWith('from ') || 
            trimmed.startsWith('#include') || trimmed.startsWith('using ')) {
            imports.push(trimmed);
        }
    }
    
    return imports;
}

function getProjectStructure() {
    // 简化的项目结构获取
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
        return {};
    }
    
    return {
        rootPath: workspaceFolders[0].uri.fsPath,
        // 可以添加更多项目结构信息
    };
}

async function insertGeneratedCode(editor: vscode.TextEditor, result: any) {
    const position = editor.selection.active;
    await editor.edit(editBuilder => {
        editBuilder.insert(position, result.code);
    });
    
    // 显示解释信息
    if (result.explanation) {
        vscode.window.showInformationMessage(
            `代码已生成。${result.explanation}`,
            '查看详情'
        ).then(selection => {
            if (selection === '查看详情') {
                showCodeExplanation({ explanation: result.explanation });
            }
        });
    }
}

async function showAnalysisResults(result: any) {
    // 创建并显示分析结果面板
    const panel = vscode.window.createWebviewPanel(
        'codeAnalysis',
        '代码分析结果',
        vscode.ViewColumn.Beside,
        { enableScripts: true }
    );
    
    panel.webview.html = generateAnalysisHtml(result);
}

async function showRefactorSuggestions(editor: vscode.TextEditor, selection: vscode.Selection, result: any) {
    const action = await vscode.window.showInformationMessage(
        '重构建议已生成',
        '应用重构',
        '查看建议',
        '取消'
    );
    
    if (action === '应用重构') {
        await editor.edit(editBuilder => {
            editBuilder.replace(selection, result.refactoredCode);
        });
    } else if (action === '查看建议') {
        const panel = vscode.window.createWebviewPanel(
            'refactorSuggestions',
            '重构建议',
            vscode.ViewColumn.Beside,
            { enableScripts: true }
        );
        panel.webview.html = generateRefactorHtml(result);
    }
}

async function createTestFile(editor: vscode.TextEditor, result: any) {
    const fileName = editor.document.fileName;
    const testFileName = generateTestFileName(fileName);
    
    const uri = vscode.Uri.file(testFileName);
    const testDocument = await vscode.workspace.openTextDocument(uri);
    const testEditor = await vscode.window.showTextDocument(testDocument);
    
    await testEditor.edit(editBuilder => {
        editBuilder.insert(new vscode.Position(0, 0), result.testCode);
    });
}

async function showCodeExplanation(result: any) {
    const panel = vscode.window.createWebviewPanel(
        'codeExplanation',
        '代码解释',
        vscode.ViewColumn.Beside,
        { enableScripts: true }
    );
    
    panel.webview.html = generateExplanationHtml(result);
}

function generateTestFileName(originalFileName: string): string {
    const path = require('path');
    const dir = path.dirname(originalFileName);
    const name = path.basename(originalFileName, path.extname(originalFileName));
    const ext = path.extname(originalFileName);
    
    return path.join(dir, `${name}.test${ext}`);
}

function generateAnalysisHtml(result: any): string {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>代码分析结果</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .issue { margin: 10px 0; padding: 10px; border-left: 4px solid #007acc; }
                .error { border-left-color: #e74c3c; }
                .warning { border-left-color: #f39c12; }
                .info { border-left-color: #3498db; }
            </style>
        </head>
        <body>
            <h1>代码分析结果</h1>
            <div id="results">
                ${JSON.stringify(result, null, 2)}
            </div>
        </body>
        </html>
    `;
}

function generateRefactorHtml(result: any): string {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>重构建议</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .code-block { background: #f4f4f4; padding: 15px; margin: 10px 0; border-radius: 5px; }
                pre { margin: 0; }
            </style>
        </head>
        <body>
            <h1>重构建议</h1>
            <h2>原代码:</h2>
            <div class="code-block">
                <pre>${result.originalCode}</pre>
            </div>
            <h2>重构后:</h2>
            <div class="code-block">
                <pre>${result.refactoredCode}</pre>
            </div>
            <h2>改进说明:</h2>
            <p>${result.explanation}</p>
        </body>
        </html>
    `;
}

function generateExplanationHtml(result: any): string {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>代码解释</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            </style>
        </head>
        <body>
            <h1>代码解释</h1>
            <div>${result.explanation}</div>
        </body>
        </html>
    `;
}

async function performAutoAnalysis(document: vscode.TextDocument) {
    if (document.languageId === 'plaintext') {
        return;
    }
    
    const code = document.getText();
    if (code.length > 10000) { // 避免分析过大的文件
        return;
    }
    
    try {
        const result = await codeAnalyzer.analyzeCode(code, document.languageId);
        
        // 如果发现严重问题，显示通知
        if (result.hasErrors) {
            vscode.window.showWarningMessage(
                '检测到代码质量问题',
                '查看详情'
            ).then(selection => {
                if (selection === '查看详情') {
                    showAnalysisResults(result);
                }
            });
        }
    } catch (error) {
        logger.error('自动分析失败:', error);
    }
}

export function deactivate() {
    logger.info('AI Code Assistant 插件正在停用...');
}
