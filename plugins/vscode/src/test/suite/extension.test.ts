import * as assert from 'assert';
import * as vscode from 'vscode';
import { ConfigurationManager } from '../../utils/configuration';
import { Logger } from '../../utils/logger';

suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    test('Configuration Manager Test', () => {
        const config = new ConfigurationManager();
        
        // 测试默认值
        const apiEndpoint = config.getApiEndpoint();
        assert.strictEqual(typeof apiEndpoint, 'string');
        
        const maxCodeLength = config.getMaxCodeLength();
        assert.strictEqual(typeof maxCodeLength, 'number');
        assert.ok(maxCodeLength > 0);
    });

    test('Logger Test', () => {
        const logger = new Logger('Test Logger');
        
        // 测试日志方法不会抛出异常
        assert.doesNotThrow(() => {
            logger.info('Test info message');
            logger.warn('Test warning message');
            logger.error('Test error message');
            logger.debug('Test debug message');
        });
        
        logger.dispose();
    });

    test('Extension Commands Registration', async () => {
        // 测试命令是否已注册
        const commands = await vscode.commands.getCommands(true);
        
        const expectedCommands = [
            'ai-assistant.generateCode',
            'ai-assistant.analyzeCode',
            'ai-assistant.refactorCode',
            'ai-assistant.generateTests',
            'ai-assistant.explainCode'
        ];
        
        expectedCommands.forEach(command => {
            assert.ok(commands.includes(command), `Command ${command} should be registered`);
        });
    });
});
