import axios, { AxiosInstance } from 'axios';
import { ConfigurationManager } from '../utils/configuration';

export interface GenerateCodeOptions {
    language: string;
    context: any;
}

export interface GenerateResult {
    code: string;
    explanation: string;
}

export interface RefactorResult {
    originalCode: string;
    refactoredCode: string;
    explanation: string;
}

export interface TestResult {
    testCode: string;
    framework: string;
    explanation: string;
}

export class CodeGeneratorClient {
    private httpClient: AxiosInstance;
    private config: ConfigurationManager;

    constructor(config: ConfigurationManager) {
        this.config = config;
        this.httpClient = axios.create({
            baseURL: config.get('apiEndpoint'),
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.get('apiKey')}`
            }
        });
    }

    async generateCode(requirement: string, language: string, context: any): Promise<GenerateResult> {
        try {
            const response = await this.httpClient.post('/api/generate', {
                requirement,
                language,
                context
            });

            return {
                code: response.data.code || '',
                explanation: response.data.explanation || '代码生成完成'
            };
        } catch (error) {
            console.error('代码生成失败:', error);
            throw new Error(`代码生成失败: ${this.getErrorMessage(error)}`);
        }
    }

    async refactorCode(code: string, language: string): Promise<RefactorResult> {
        try {
            const response = await this.httpClient.post('/api/refactor', {
                code,
                language,
                refactor_type: 'general'
            });

            return {
                originalCode: code,
                refactoredCode: response.data.refactored_code || code,
                explanation: response.data.explanation || '重构完成'
            };
        } catch (error) {
            console.error('代码重构失败:', error);
            throw new Error(`代码重构失败: ${this.getErrorMessage(error)}`);
        }
    }

    async generateTests(code: string, language: string): Promise<TestResult> {
        try {
            const response = await this.httpClient.post('/api/generate-tests', {
                code,
                language,
                test_framework: this.getDefaultTestFramework(language)
            });

            return {
                testCode: response.data.test_code || '',
                framework: response.data.framework || 'default',
                explanation: response.data.explanation || '测试用例生成完成'
            };
        } catch (error) {
            console.error('测试生成失败:', error);
            throw new Error(`测试生成失败: ${this.getErrorMessage(error)}`);
        }
    }

    updateConfig(config: ConfigurationManager): void {
        this.config = config;
        this.httpClient.defaults.baseURL = config.get('apiEndpoint');
        this.httpClient.defaults.headers['Authorization'] = `Bearer ${config.get('apiKey')}`;
    }

    private getDefaultTestFramework(language: string): string {
        const frameworks: { [key: string]: string } = {
            'javascript': 'jest',
            'typescript': 'jest',
            'python': 'pytest',
            'java': 'junit',
            'csharp': 'nunit',
            'go': 'testing',
            'rust': 'cargo-test'
        };
        return frameworks[language] || 'default';
    }

    private getErrorMessage(error: any): string {
        if (error.response) {
            return error.response.data?.message || error.response.statusText || '服务器错误';
        } else if (error.request) {
            return '网络连接失败，请检查API服务器是否运行';
        } else {
            return error.message || '未知错误';
        }
    }
}
