import axios, { AxiosInstance } from 'axios';
import { ConfigurationManager } from '../utils/configuration';

export interface AnalysisResult {
    hasErrors: boolean;
    issues: Issue[];
    metrics: CodeMetrics;
    suggestions: string[];
}

export interface Issue {
    type: 'error' | 'warning' | 'info';
    message: string;
    line?: number;
    column?: number;
    severity: 'high' | 'medium' | 'low';
}

export interface CodeMetrics {
    complexity: number;
    maintainability: number;
    testability: number;
    linesOfCode: number;
}

export interface ExplanationResult {
    explanation: string;
    concepts: string[];
    complexity: 'low' | 'medium' | 'high';
}

export class CodeAnalyzerClient {
    private httpClient: AxiosInstance;
    private config: ConfigurationManager;

    constructor(config: ConfigurationManager) {
        this.config = config;
        this.httpClient = axios.create({
            baseURL: config.get('apiEndpoint'),
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.get('apiKey')}`
            }
        });
    }

    async analyzeCode(code: string, language: string): Promise<AnalysisResult> {
        try {
            const response = await this.httpClient.post('/api/analyze', {
                code,
                language,
                options: {
                    checkSyntax: true,
                    checkQuality: true,
                    checkSecurity: true,
                    checkPerformance: true
                }
            });

            const data = response.data.analysis || {};
            
            return {
                hasErrors: this.hasErrors(data),
                issues: this.parseIssues(data),
                metrics: this.parseMetrics(data),
                suggestions: data.suggestions || []
            };
        } catch (error) {
            console.error('代码分析失败:', error);
            throw new Error(`代码分析失败: ${this.getErrorMessage(error)}`);
        }
    }

    async explainCode(code: string, language: string): Promise<ExplanationResult> {
        try {
            const response = await this.httpClient.post('/api/explain', {
                code,
                language,
                detail_level: 'medium'
            });

            return {
                explanation: response.data.explanation || '无法解释此代码',
                concepts: response.data.concepts || [],
                complexity: response.data.complexity || 'medium'
            };
        } catch (error) {
            console.error('代码解释失败:', error);
            throw new Error(`代码解释失败: ${this.getErrorMessage(error)}`);
        }
    }

    updateConfig(config: ConfigurationManager): void {
        this.config = config;
        this.httpClient.defaults.baseURL = config.get('apiEndpoint');
        this.httpClient.defaults.headers['Authorization'] = `Bearer ${config.get('apiKey')}`;
    }

    private hasErrors(data: any): boolean {
        const syntaxIssues = data.syntax_issues || [];
        const qualityIssues = data.quality_issues || [];
        const securityIssues = data.security_issues || [];
        
        return syntaxIssues.some((issue: any) => issue.severity === 'error') ||
               qualityIssues.some((issue: any) => issue.severity === 'high') ||
               securityIssues.length > 0;
    }

    private parseIssues(data: any): Issue[] {
        const issues: Issue[] = [];
        
        // 解析语法问题
        if (data.syntax_issues) {
            data.syntax_issues.forEach((issue: any) => {
                issues.push({
                    type: issue.severity === 'error' ? 'error' : 'warning',
                    message: issue.message || '语法问题',
                    line: issue.line,
                    column: issue.column,
                    severity: this.mapSeverity(issue.severity)
                });
            });
        }

        // 解析质量问题
        if (data.quality_issues) {
            data.quality_issues.forEach((issue: any) => {
                issues.push({
                    type: 'warning',
                    message: issue.message || '代码质量问题',
                    line: issue.line,
                    column: issue.column,
                    severity: this.mapSeverity(issue.severity)
                });
            });
        }

        // 解析安全问题
        if (data.security_issues) {
            data.security_issues.forEach((issue: any) => {
                issues.push({
                    type: 'error',
                    message: issue.message || '安全问题',
                    line: issue.line,
                    column: issue.column,
                    severity: 'high'
                });
            });
        }

        return issues;
    }

    private parseMetrics(data: any): CodeMetrics {
        const metrics = data.metrics || {};
        
        return {
            complexity: metrics.complexity || 0,
            maintainability: metrics.maintainability || 0,
            testability: metrics.testability || 0,
            linesOfCode: metrics.lines_of_code || 0
        };
    }

    private mapSeverity(severity: string): 'high' | 'medium' | 'low' {
        switch (severity?.toLowerCase()) {
            case 'error':
            case 'critical':
                return 'high';
            case 'warning':
            case 'major':
                return 'medium';
            case 'info':
            case 'minor':
            default:
                return 'low';
        }
    }

    private getErrorMessage(error: any): string {
        if (error.response) {
            return error.response.data?.message || error.response.statusText || '服务器错误';
        } else if (error.request) {
            return '网络连接失败，请检查API服务器是否运行';
        } else {
            return error.message || '未知错误';
        }
    }
}
