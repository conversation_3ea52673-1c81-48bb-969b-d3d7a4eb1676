"""
AI Code Assistant - 核心API服务器
提供代码生成、分析、重构等功能的RESTful API
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
import asyncio
import logging
from datetime import datetime
import uuid

from .services.code_generator import CodeGeneratorService
from .services.code_analyzer import CodeAnalyzerService
from .services.nlp_processor import NLPProcessorService
from .utils.auth import verify_api_key
from .utils.rate_limiter import RateLimiter
from .utils.cache import CacheManager
from .models.requests import *
from .models.responses import *

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="AI Code Assistant API",
    description="智能代码生成与分析服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全认证
security = HTTPBearer()

# 初始化服务
code_generator = CodeGeneratorService()
code_analyzer = CodeAnalyzerService()
nlp_processor = NLPProcessorService()
rate_limiter = RateLimiter()
cache_manager = CacheManager()

# 依赖注入
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证API密钥"""
    try:
        user_info = await verify_api_key(credentials.credentials)
        return user_info
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid API key")

async def check_rate_limit(user_info: dict = Depends(get_current_user)):
    """检查请求频率限制"""
    user_id = user_info.get("user_id")
    if not await rate_limiter.check_limit(user_id):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    return user_info

# API路由

@app.get("/")
async def root():
    """健康检查端点"""
    return {
        "message": "AI Code Assistant API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/api/generate", response_model=GenerateResponse)
async def generate_code(
    request: GenerateRequest,
    background_tasks: BackgroundTasks,
    user_info: dict = Depends(check_rate_limit)
):
    """生成代码"""
    try:
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        logger.info(f"代码生成请求 {request_id}: {request.requirement[:100]}...")
        
        # 检查缓存
        cache_key = f"generate:{hash(request.requirement + request.language)}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            logger.info(f"返回缓存结果 {request_id}")
            return GenerateResponse(**cached_result)
        
        # 处理需求
        processed_req = await nlp_processor.process_requirement(
            request.requirement,
            request.context
        )
        
        # 生成代码
        result = await code_generator.generate_code(
            processed_req,
            request.language,
            request.context
        )
        
        # 缓存结果
        background_tasks.add_task(
            cache_manager.set,
            cache_key,
            result.dict(),
            expire_seconds=3600
        )
        
        # 记录使用统计
        background_tasks.add_task(
            log_usage,
            user_info["user_id"],
            "generate",
            request_id
        )
        
        logger.info(f"代码生成完成 {request_id}")
        return result
        
    except Exception as e:
        logger.error(f"代码生成失败 {request_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Code generation failed: {str(e)}")

@app.post("/api/analyze", response_model=AnalyzeResponse)
async def analyze_code(
    request: AnalyzeRequest,
    background_tasks: BackgroundTasks,
    user_info: dict = Depends(check_rate_limit)
):
    """分析代码质量"""
    try:
        request_id = str(uuid.uuid4())
        
        logger.info(f"代码分析请求 {request_id}: {request.language}")
        
        # 检查代码长度限制
        if len(request.code) > 50000:
            raise HTTPException(status_code=400, detail="Code too long (max 50000 characters)")
        
        # 检查缓存
        cache_key = f"analyze:{hash(request.code + request.language)}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            logger.info(f"返回缓存的分析结果 {request_id}")
            return AnalyzeResponse(**cached_result)
        
        # 执行分析
        result = await code_analyzer.analyze_code(
            request.code,
            request.language,
            request.options
        )
        
        # 缓存结果
        background_tasks.add_task(
            cache_manager.set,
            cache_key,
            result.dict(),
            expire_seconds=1800
        )
        
        # 记录使用统计
        background_tasks.add_task(
            log_usage,
            user_info["user_id"],
            "analyze",
            request_id
        )
        
        logger.info(f"代码分析完成 {request_id}")
        return result
        
    except Exception as e:
        logger.error(f"代码分析失败 {request_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Code analysis failed: {str(e)}")

@app.post("/api/refactor", response_model=RefactorResponse)
async def refactor_code(
    request: RefactorRequest,
    background_tasks: BackgroundTasks,
    user_info: dict = Depends(check_rate_limit)
):
    """重构代码"""
    try:
        request_id = str(uuid.uuid4())
        
        logger.info(f"代码重构请求 {request_id}: {request.language}")
        
        # 执行重构
        result = await code_generator.refactor_code(
            request.code,
            request.language,
            request.refactor_type,
            request.options
        )
        
        # 记录使用统计
        background_tasks.add_task(
            log_usage,
            user_info["user_id"],
            "refactor",
            request_id
        )
        
        logger.info(f"代码重构完成 {request_id}")
        return result
        
    except Exception as e:
        logger.error(f"代码重构失败 {request_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Code refactoring failed: {str(e)}")

@app.post("/api/generate-tests", response_model=GenerateTestsResponse)
async def generate_tests(
    request: GenerateTestsRequest,
    background_tasks: BackgroundTasks,
    user_info: dict = Depends(check_rate_limit)
):
    """生成测试用例"""
    try:
        request_id = str(uuid.uuid4())
        
        logger.info(f"测试生成请求 {request_id}: {request.language}")
        
        # 生成测试用例
        result = await code_generator.generate_tests(
            request.code,
            request.language,
            request.test_framework,
            request.options
        )
        
        # 记录使用统计
        background_tasks.add_task(
            log_usage,
            user_info["user_id"],
            "generate_tests",
            request_id
        )
        
        logger.info(f"测试生成完成 {request_id}")
        return result
        
    except Exception as e:
        logger.error(f"测试生成失败 {request_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Test generation failed: {str(e)}")

@app.post("/api/explain", response_model=ExplainResponse)
async def explain_code(
    request: ExplainRequest,
    background_tasks: BackgroundTasks,
    user_info: dict = Depends(check_rate_limit)
):
    """解释代码"""
    try:
        request_id = str(uuid.uuid4())
        
        logger.info(f"代码解释请求 {request_id}: {request.language}")
        
        # 检查缓存
        cache_key = f"explain:{hash(request.code + request.language)}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            logger.info(f"返回缓存的解释结果 {request_id}")
            return ExplainResponse(**cached_result)
        
        # 执行解释
        result = await code_analyzer.explain_code(
            request.code,
            request.language,
            request.detail_level
        )
        
        # 缓存结果
        background_tasks.add_task(
            cache_manager.set,
            cache_key,
            result.dict(),
            expire_seconds=3600
        )
        
        # 记录使用统计
        background_tasks.add_task(
            log_usage,
            user_info["user_id"],
            "explain",
            request_id
        )
        
        logger.info(f"代码解释完成 {request_id}")
        return result
        
    except Exception as e:
        logger.error(f"代码解释失败 {request_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Code explanation failed: {str(e)}")

@app.get("/api/languages")
async def get_supported_languages():
    """获取支持的编程语言列表"""
    return {
        "languages": [
            {"id": "python", "name": "Python", "extensions": [".py"]},
            {"id": "javascript", "name": "JavaScript", "extensions": [".js", ".mjs"]},
            {"id": "typescript", "name": "TypeScript", "extensions": [".ts"]},
            {"id": "java", "name": "Java", "extensions": [".java"]},
            {"id": "csharp", "name": "C#", "extensions": [".cs"]},
            {"id": "go", "name": "Go", "extensions": [".go"]},
            {"id": "rust", "name": "Rust", "extensions": [".rs"]},
            {"id": "cpp", "name": "C++", "extensions": [".cpp", ".cc", ".cxx"]},
            {"id": "c", "name": "C", "extensions": [".c"]},
            {"id": "php", "name": "PHP", "extensions": [".php"]},
            {"id": "ruby", "name": "Ruby", "extensions": [".rb"]},
            {"id": "swift", "name": "Swift", "extensions": [".swift"]},
            {"id": "kotlin", "name": "Kotlin", "extensions": [".kt"]},
            {"id": "scala", "name": "Scala", "extensions": [".scala"]},
        ]
    }

@app.get("/api/stats")
async def get_usage_stats(user_info: dict = Depends(get_current_user)):
    """获取用户使用统计"""
    try:
        stats = await get_user_stats(user_info["user_id"])
        return stats
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get usage stats")

# 后台任务

async def log_usage(user_id: str, action: str, request_id: str):
    """记录使用统计"""
    try:
        # 这里可以记录到数据库或日志系统
        logger.info(f"用户 {user_id} 执行 {action} 操作，请求ID: {request_id}")
        # 实际实现中可以写入数据库
    except Exception as e:
        logger.error(f"记录使用统计失败: {str(e)}")

async def get_user_stats(user_id: str):
    """获取用户统计信息"""
    # 这里应该从数据库获取实际统计数据
    return {
        "user_id": user_id,
        "total_requests": 0,
        "requests_today": 0,
        "favorite_language": "python",
        "success_rate": 0.95
    }

# 异常处理

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return {
        "error": True,
        "status_code": exc.status_code,
        "message": exc.detail,
        "timestamp": datetime.now().isoformat()
    }

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"未处理的异常: {str(exc)}")
    return {
        "error": True,
        "status_code": 500,
        "message": "Internal server error",
        "timestamp": datetime.now().isoformat()
    }

# 启动事件

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("AI Code Assistant API 正在启动...")
    
    # 初始化服务
    await code_generator.initialize()
    await code_analyzer.initialize()
    await nlp_processor.initialize()
    
    logger.info("AI Code Assistant API 启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("AI Code Assistant API 正在关闭...")
    
    # 清理资源
    await code_generator.cleanup()
    await code_analyzer.cleanup()
    await nlp_processor.cleanup()
    
    logger.info("AI Code Assistant API 已关闭")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "core.api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
